Android 13 行为变更
提取时间: 2025-09-02T10:58:08.335763
来源: https://developer.android.com/about/versions/13/behavior-changes-all?hl=zh-cn
================================================================================

Cloud Translation API
Android Developers
使用集合让一切井井有条
根据您的偏好保存内容并对其进行分类。
Android 13 平台包含一些可能会影响您的应用的行为变更。以下行为变更会影响在 Android 13 上运行的所有应用，无论采用哪种
targetSdkVersion
您应该测试您的应用，然后根据需要进行修改，以适当地支持这些变更。
仅影响以 Android 13 为目标平台的应用的行为变更
任务管理器的工作流，可允许用户停止具有持续前台服务的应用。此工作流只会出现在搭载 Android 13 或更高版本的设备上。
从 Android 13（API 级别 33）开始，用户可以通过抽屉式通知栏完成工作流，以停止具有持续前台服务的应用，如图 1 所示。此功能称为
处理这种由用户发起的停止操作
使用 JobScheduler 改进预提取作业处理
利用 JobScheduler，应用可使用
JobInfo.Builder.setPrefetch()
将特定作业标记为“预提取”作业，这意味着，理想情况下这些作业应该在应用下一次启动前提前一点运行，以提升用户体验。过去，JobScheduler 仅使用该信号让预提取作业有机会使用免费或多余的数据。
在 Android 13（API 级别 33）及更高版本中，系统会尝试确定应用下次启动的时间，并根据该估算时间来运行预提取作业。应用应尝试使用预提取作业来完成他们想要在下次应用启动前完成的任何工作。
Android 13（API 级别 33）支持系统通过以下方式来更有效地管理设备电池续航时间：
更新了有关系统何时将您的应用放入
“受限”应用待机模式存储分区
对于您的应用在以下情况下可以执行的操作制定了新限制：用户因您应用的后台电池用量过高而将其置于
在测试应用时，请务必检查以下事项：
测试您的应用在系统将其放入
“受限”应用待机模式存储分区
时的响应方式。使用以下 Android 调试桥 (ADB) 命令将应用分配到此分桶：
adb shell am set-standby-bucket
PACKAGE_NAME
测试应用对以下常见限制的响应情况，这些限制通常适用于因后台电池用量过高而处于
现有的前台服务会从前台移除
使用以下 ADB 命令将应用置于“受限”状态：
adb shell cmd appops set
PACKAGE_NAME
RUN_ANY_IN_BACKGROUND ignore
高优先级 Firebase Cloud Message (FCM) 配额
Android 13（API 级别 33）更新了
Firebase Cloud Messaging
(FCM) 配额，从而提高了针对高优先级 FCM 显示通知的高优先级 FCM 传送的可靠性。Android 13（API 级别 33）中发生了以下变更：
不再决定应用可以使用多少个高优先级 FCM。
如果系统检测到应用持续发送不会生成通知的高优先级消息，现在会降低这些消息的优先级。
与以前的 Android 版本一样，超出配额的高优先级 FCM 会降级为普通优先级。为了响应 FCM 而启动
(FGS) 时，我们建议您检查
RemoteMessage.getPriority()
PRIORITY_HIGH
，并且/或者处理任何潜在
ForegroundServiceStartNotAllowedException
如果您的应用并非始终为了响应高优先级 FCM 而发布通知，我们建议您将这些 FCM 的优先级更改为
，这样生成通知的消息就不会降级。
Android 13（API 级别 33）引入了运行时
POST_NOTIFICATIONS
此更改有助于用户专注于最重要的通知。
强烈建议您尽快以 Android 13 或更高版本为目标平台，以获享此功能提供的额外控制和灵活性。
从剪贴板中隐藏敏感内容
如果您的应用允许用户将敏感内容（例如密码或信用卡信息）复制到剪贴板，则必须在调用
ClipboardManager#setPrimaryClip()
之前向 ClipData 的
ClipDescription
添加一个标志。添加此标志可阻止敏感内容出现在内容预览中。
所复制文本的预览（未标记敏感内容）。
所复制文本的预览（已标记敏感内容）。
如需标记敏感内容，请向
ClipDescription
添加一个布尔型 extra。无论应用的目标 API 级别如何，所有应用都应这么做。
// When your app is compiled with the API level 33 SDK or higher
description
PersistableBundle
ClipDescription
EXTRA_IS_SENSITIVE
// If your app is compiled with a lower SDK
description
PersistableBundle
"android.content.extra.IS_SENSITIVE"
如需详细了解新的剪贴板界面，请访问
停止使用共享用户 ID
如果您的应用使用已废弃的
android:sharedUserId
属性，并且不再依赖于该属性的功能，您可以将
android:sharedUserMaxSdkVersion
compatibility,
"android:sharedUserId"
android:sharedUserId="
SHARED_PACKAGE_NAME
android:sharedUserMaxSdkVersion="32"
</manifest>
这个属性会告知系统，您的应用不再依赖于共享用户 ID。如果您的应用声明
android:sharedUserMaxSdkVersion
并且首次安装在搭载 Android 13 或更高版本的设备上，则应用的行为就像您从未定义过
android:sharedUserId
一样。更新后的应用仍会使用现有的共享用户 ID。
共享用户 ID 会在软件包管理器中导致具有不确定性的行为。您的应用应使用适当的通信机制（例如服务和 content provider），在共享组件之间实现互操作性。
在搭载 Android 13 或更高版本的设备上，
用户可以默认关闭与前台服务相关联的通知
移除了旧版语音服务实现副本
Android 13 从 Google 应用中移除了
SpeechService
实现，包括 Voice IME、
RecognitionService
基于 intent 的 API
在 Android 12 中发生了以下变更：
SpeechService
Google 语音服务应用
SpeechService
RecognitionService
功能已移至 Android System Intelligence 应用，以支持设备端语音识别。
为了帮助在 Android 12 上保持应用兼容性，Google 应用会使用 trampoline 将流量引导至 Google 语音服务应用。在 Android 13 中，此 trampoline 已被移除。
SpeechService
默认提供程序，而不是硬编码为特定应用。
本页面上的内容和代码示例受
部分所述许可的限制。Java 和 OpenJDK 是 Oracle 和/或其关联公司的注册商标。
最后更新时间 (UTC)：2025-08-27。
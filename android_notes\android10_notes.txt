Android 10 行为变更
提取时间: 2025-09-02T10:58:16.465680
来源: https://developer.android.com/about/versions/10/behavior-changes-all?hl=zh-cn
================================================================================

KeyChain 行为变更和改进
当 TLS 服务器在 TLS 握手中发送证书请求消息时，某些浏览器（如 Google Chrome）允许用户选择证书。从 Android 10 开始，KeyChain对象会在调用KeyChain.choosePrivateKeyAlias()时信任颁发机构和密钥规范参数，以向用户显示证书选择提示。需要注意的是，此提示不包含不符合服务器规范的选项。
如果没有可供用户选择的证书（例如，没有与服务器规范匹配的证书，或者设备上未安装任何证书），则完全不会出现证书选择提示。
此外，在 Android 10 或更高版本上，无需具备设备屏幕锁定功能，就能将密钥或 CA 证书导入KeyChain对象中。

java.math.BigDecimal.stripTrailingZeros() 行为变更
如果输入值为零，BigDecimal.stripTrailingZeros()不再将尾随零作为特殊情况保留。

java.util.regex.Matcher 和 Pattern 行为变更
当输入开头存在零宽度匹配时，split()的结果已更改为不再以空String（“”）开头。这也会影响String.split()。例如，"x".split("")现在返回{"x"}，而在旧版 Android 上，它过去返回{"", "x"}。"aardvark".split("(?=a)"现在返回{"a", "ardv", "ark"}，而不是{"", "a", "ardv", "ark"}。
还改进了无效实参的异常行为：
如果替换String以单独的反斜杠结尾（这是不允许的），appendReplacement(StringBuffer, String)现在会抛出IllegalArgumentException而不是IndexOutOfBoundsException。如果替换的String以$结尾，则现在会抛出相同的异常。之前，在这种情况下不会抛出任何异常。如果Matcher抛出NullPointerException，replaceFirst(null)不再在Matcher上调用reset()。现在，如果没有匹配项，也会抛出NullPointerException。之前，仅在存在匹配项时才会抛出此异常。start(int group)、end(int group)和group(int group)现在会在组索引超出范围时抛出更一般的IndexOutOfBoundsException。
之前，这些方法会抛出ArrayIndexOutOfBoundsException。